/**
 * 音频播放管理器
 * 负责音频播放控制、打断检测和播放队列管理
 */

export interface PlaybackConfig {
  interruptionThreshold?: number // 打断音量阈值
  interruptionDelay?: number // 打断延迟（毫秒）
  fadeOutDuration?: number // 淡出时长（毫秒）
  maxConcurrentPlaybacks?: number // 最大同时播放数量
}

export interface PlaybackInfo {
  id: string
  startTime: number
  duration?: number
  source: AudioBufferSourceNode
  gainNode: GainNode
  isPlaying: boolean
  canBeInterrupted: boolean
}

export interface PlaybackEvent {
  type: 'started' | 'ended' | 'interrupted' | 'error'
  playbackId: string
  timestamp: number
  data?: any
}

export class AudioPlaybackManager {
  private audioContext: AudioContext | null = null
  private activePlaybacks = new Map<string, PlaybackInfo>()
  private config: Required<PlaybackConfig>
  private isInterruptionEnabled = true
  private lastInterruptionTime = 0

  // 事件回调
  private onPlaybackEventCallback?: (event: PlaybackEvent) => void
  private onInterruptionCallback?: (reason: 'volume' | 'manual', playbackIds: string[]) => void

  constructor(config: PlaybackConfig = {}) {
    this.config = {
      interruptionThreshold: config.interruptionThreshold || 30,
      interruptionDelay: config.interruptionDelay || 200,
      fadeOutDuration: config.fadeOutDuration || 100,
      maxConcurrentPlaybacks: config.maxConcurrentPlaybacks || 3,
    }
  }

  /**
   * 初始化音频上下文
   */
  async initAudioContext(): Promise<void> {
    if (this.audioContext && this.audioContext.state !== 'closed') {
      return
    }

    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }
    } catch (error) {
      throw new Error(`音频上下文初始化失败: ${error}`)
    }
  }

  /**
   * 播放音频数据
   */
  async playAudio(
    audioData: ArrayBuffer,
    options: {
      id?: string
      canBeInterrupted?: boolean
      volume?: number
      onEnd?: () => void
    } = {},
  ): Promise<string> {
    if (!this.audioContext) {
      await this.initAudioContext()
    }

    if (!this.audioContext) {
      throw new Error('音频上下文未初始化')
    }

    // 检查并发播放限制
    if (this.activePlaybacks.size >= this.config.maxConcurrentPlaybacks) {
      // 停止最早的播放
      const oldestPlayback = Array.from(this.activePlaybacks.values()).sort(
        (a, b) => a.startTime - b.startTime,
      )[0]
      if (oldestPlayback) {
        this.stopPlayback(oldestPlayback.id, 'replaced')
      }
    }

    const playbackId = options.id || this.generatePlaybackId()

    try {
      // 解码音频数据
      const audioBuffer = await this.audioContext.decodeAudioData(audioData.slice(0))

      // 创建音频源和增益节点
      const source = this.audioContext.createBufferSource()
      const gainNode = this.audioContext.createGain()

      source.buffer = audioBuffer
      gainNode.gain.value = options.volume || 1.0

      // 连接音频节点
      source.connect(gainNode)
      gainNode.connect(this.audioContext.destination)

      // 创建播放信息
      const playbackInfo: PlaybackInfo = {
        id: playbackId,
        startTime: Date.now(),
        duration: audioBuffer.duration * 1000, // 转换为毫秒
        source,
        gainNode,
        isPlaying: true,
        canBeInterrupted: options.canBeInterrupted !== false,
      }

      // 设置播放结束回调
      source.onended = () => {
        this.handlePlaybackEnd(playbackId, 'natural')
        if (options.onEnd) {
          options.onEnd()
        }
      }

      // 开始播放
      source.start()
      this.activePlaybacks.set(playbackId, playbackInfo)

      // 触发播放开始事件
      this.emitPlaybackEvent({
        type: 'started',
        playbackId,
        timestamp: Date.now(),
      })

      return playbackId
    } catch (error) {
      this.emitPlaybackEvent({
        type: 'error',
        playbackId,
        timestamp: Date.now(),
        data: { error: error instanceof Error ? error.message : String(error) },
      })
      throw new Error(`音频播放失败: ${error}`)
    }
  }

  /**
   * 停止指定播放
   */
  stopPlayback(
    playbackId: string,
    reason: 'manual' | 'interrupted' | 'replaced' = 'manual',
  ): boolean {
    const playback = this.activePlaybacks.get(playbackId)
    if (!playback || !playback.isPlaying) {
      return false
    }

    try {
      // 如果需要淡出效果
      if (this.config.fadeOutDuration > 0 && reason === 'interrupted') {
        this.fadeOut(playback)
      } else {
        // 立即停止
        playback.source.stop()
      }

      playback.isPlaying = false
      this.handlePlaybackEnd(playbackId, reason)
      return true
    } catch (error) {
      console.error(`停止播放失败: ${error}`)
      return false
    }
  }

  /**
   * 停止所有播放
   */
  stopAllPlaybacks(reason: 'manual' | 'interrupted' = 'manual'): string[] {
    const stoppedIds: string[] = []

    for (const [id, playback] of this.activePlaybacks) {
      if (playback.isPlaying) {
        if (this.stopPlayback(id, reason)) {
          stoppedIds.push(id)
        }
      }
    }

    return stoppedIds
  }

  /**
   * 检查音量并处理打断
   */
  checkVolumeInterruption(volume: number): boolean {
    if (!this.isInterruptionEnabled || volume < this.config.interruptionThreshold) {
      return false
    }

    // 防止频繁打断
    const now = Date.now()
    if (now - this.lastInterruptionTime < this.config.interruptionDelay) {
      return false
    }

    // 找到可以被打断的播放
    const interruptiblePlaybacks = Array.from(this.activePlaybacks.entries())
      .filter(([_, playback]) => playback.isPlaying && playback.canBeInterrupted)
      .map(([id, _]) => id)

    if (interruptiblePlaybacks.length === 0) {
      return false
    }

    // 停止这些播放
    const stoppedIds = interruptiblePlaybacks.filter((id) => this.stopPlayback(id, 'interrupted'))

    if (stoppedIds.length > 0) {
      this.lastInterruptionTime = now

      // 触发打断回调
      if (this.onInterruptionCallback) {
        this.onInterruptionCallback('volume', stoppedIds)
      }

      return true
    }

    return false
  }

  /**
   * 手动触发打断
   */
  manualInterrupt(): string[] {
    const stoppedIds = this.stopAllPlaybacks('interrupted')

    if (stoppedIds.length > 0 && this.onInterruptionCallback) {
      this.onInterruptionCallback('manual', stoppedIds)
    }

    return stoppedIds
  }

  /**
   * 淡出效果
   */
  private fadeOut(playback: PlaybackInfo): void {
    if (!this.audioContext) return

    const now = this.audioContext.currentTime
    const fadeTime = this.config.fadeOutDuration / 1000 // 转换为秒

    // 设置音量淡出
    playback.gainNode.gain.setValueAtTime(playback.gainNode.gain.value, now)
    playback.gainNode.gain.linearRampToValueAtTime(0, now + fadeTime)

    // 在淡出完成后停止播放
    setTimeout(() => {
      try {
        playback.source.stop()
      } catch (error) {
        // 忽略已停止的音频源错误
      }
    }, this.config.fadeOutDuration)
  }

  /**
   * 处理播放结束
   */
  private handlePlaybackEnd(
    playbackId: string,
    reason: 'natural' | 'manual' | 'interrupted' | 'replaced',
  ): void {
    const playback = this.activePlaybacks.get(playbackId)
    if (playback) {
      playback.isPlaying = false
      this.activePlaybacks.delete(playbackId)

      // 触发结束事件
      const eventType = reason === 'interrupted' ? 'interrupted' : 'ended'
      this.emitPlaybackEvent({
        type: eventType,
        playbackId,
        timestamp: Date.now(),
        data: { reason },
      })
    }
  }

  /**
   * 生成播放ID
   */
  private generatePlaybackId(): string {
    return `playback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 触发播放事件
   */
  private emitPlaybackEvent(event: PlaybackEvent): void {
    if (this.onPlaybackEventCallback) {
      this.onPlaybackEventCallback(event)
    }
  }

  /**
   * 设置播放事件回调
   */
  onPlaybackEvent(callback: (event: PlaybackEvent) => void): void {
    this.onPlaybackEventCallback = callback
  }

  /**
   * 设置打断回调
   */
  onInterruption(callback: (reason: 'volume' | 'manual', playbackIds: string[]) => void): void {
    this.onInterruptionCallback = callback
  }

  /**
   * 启用/禁用打断功能
   */
  setInterruptionEnabled(enabled: boolean): void {
    this.isInterruptionEnabled = enabled
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<PlaybackConfig>): void {
    this.config = { ...this.config, ...config }
  }

  /**
   * 获取当前播放状态
   */
  getPlaybackStatus() {
    const activePlaybacks = Array.from(this.activePlaybacks.values()).filter((p) => p.isPlaying)

    return {
      activeCount: activePlaybacks.length,
      totalPlaybacks: this.activePlaybacks.size,
      isAnyPlaying: activePlaybacks.length > 0,
      playbackIds: activePlaybacks.map((p) => p.id),
      interruptionEnabled: this.isInterruptionEnabled,
      config: this.config,
    }
  }

  /**
   * 获取播放详情
   */
  getPlaybackInfo(playbackId: string): PlaybackInfo | undefined {
    return this.activePlaybacks.get(playbackId)
  }

  /**
   * 获取所有播放信息
   */
  getAllPlaybackInfo(): PlaybackInfo[] {
    return Array.from(this.activePlaybacks.values())
  }

  /**
   * 暂停播放
   */
  pausePlayback(playbackId: string): boolean {
    const playback = this.activePlaybacks.get(playbackId)
    if (!playback || !playback.isPlaying) {
      return false
    }

    try {
      // Web Audio API 不支持直接暂停，这里使用静音实现
      playback.gainNode.gain.value = 0
      return true
    } catch (error) {
      console.error(`暂停播放失败: ${error}`)
      return false
    }
  }

  /**
   * 恢复播放
   */
  resumePlayback(playbackId: string, volume: number = 1.0): boolean {
    const playback = this.activePlaybacks.get(playbackId)
    if (!playback || !playback.isPlaying) {
      return false
    }

    try {
      playback.gainNode.gain.value = volume
      return true
    } catch (error) {
      console.error(`恢复播放失败: ${error}`)
      return false
    }
  }

  /**
   * 清理资源
   */
  destroy(): void {
    // 停止所有播放
    this.stopAllPlaybacks('manual')

    // 关闭音频上下文
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close()
      this.audioContext = null
    }

    // 清理回调
    this.onPlaybackEventCallback = undefined
    this.onInterruptionCallback = undefined

    // 清理播放信息
    this.activePlaybacks.clear()
  }
}
