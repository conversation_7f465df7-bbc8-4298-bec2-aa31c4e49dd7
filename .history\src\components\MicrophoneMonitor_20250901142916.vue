<template>
  <div class="microphone-monitor">
    <div class="monitor-header">
      <h2>🎤 实时麦克风音量监听</h2>
      <p class="description">实时获取麦克风数据并显示音量大小</p>
    </div>

    <div class="control-panel">
      <button
        v-if="!hasPermission"
        @click="requestPermission"
        class="permission-btn"
        :disabled="isRequesting"
      >
        {{ isRequesting ? '申请中...' : '申请麦克风权限' }}
      </button>

      <div v-if="hasPermission" class="recording-controls">
        <button @click="toggleMonitoring" :class="['monitor-btn', { active: isMonitoring }]">
          {{ isMonitoring ? '停止监听' : '开始监听' }}
        </button>
      </div>
    </div>

    <div v-if="hasPermission" class="volume-display">
      <div class="volume-info">
        <div class="volume-value">
          <span class="label">当前音量:</span>
          <span class="value">{{ currentVolume.toFixed(1) }}%</span>
        </div>
        <div class="volume-status" :class="{ active: isVolumeActive }">
          {{ isVolumeActive ? '🔊 有声音' : '🔇 静音' }}
        </div>
      </div>

      <div class="volume-visualizer">
        <div class="volume-bar-container">
          <div class="volume-bar">
            <div class="volume-fill" :style="{ width: currentVolume + '%' }"></div>
          </div>
          <div class="volume-scale">
            <span v-for="i in 5" :key="i" class="scale-mark">{{ i * 20 }}</span>
          </div>
        </div>

        <div class="volume-circle">
          <svg width="120" height="120" viewBox="0 0 120 120">
            <circle cx="60" cy="60" r="50" fill="none" stroke="#e0e0e0" stroke-width="8" />
            <circle
              cx="60"
              cy="60"
              r="50"
              fill="none"
              :stroke="volumeColor"
              stroke-width="8"
              stroke-linecap="round"
              :stroke-dasharray="circumference"
              :stroke-dashoffset="circumference - (currentVolume / 100) * circumference"
              transform="rotate(-90 60 60)"
            />
            <text
              x="60"
              y="65"
              text-anchor="middle"
              font-size="16"
              font-weight="bold"
              :fill="volumeColor"
            >
              {{ Math.round(currentVolume) }}%
            </text>
          </svg>
        </div>
      </div>
    </div>

    <div class="status-info">
      <div class="status-item">
        <span class="status-label">权限状态:</span>
        <span :class="['status-value', hasPermission ? 'success' : 'error']">
          {{ hasPermission ? '✅ 已授权' : '❌ 未授权' }}
        </span>
      </div>
      <div class="status-item">
        <span class="status-label">监听状态:</span>
        <span :class="['status-value', isMonitoring ? 'success' : 'inactive']">
          {{ isMonitoring ? '🎵 监听中' : '⏸️ 已停止' }}
        </span>
      </div>
    </div>

    <div v-if="error" class="error-message">❌ {{ error }}</div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onUnmounted } from 'vue'

export default defineComponent({
  name: 'MicrophoneMonitor',
  setup() {
    // 将所有现有的响应式变量和函数移到这里
    const hasPermission = ref(false)
    const isRequesting = ref(false)
    const isMonitoring = ref(false)
    const currentVolume = ref(0)
    const error = ref('')

    // 音频相关变量
    let audioContext: AudioContext | null = null
    let mediaStream: MediaStream | null = null
    let analyserNode: AnalyserNode | null = null
    let sourceNode: MediaStreamAudioSourceNode | null = null
    let animationFrame: number | null = null

    // 计算属性
    const isVolumeActive = computed(() => currentVolume.value > 5)
    const circumference = computed(() => 2 * Math.PI * 50)
    const volumeColor = computed(() => {
      if (currentVolume.value < 20) return '#4CAF50'
      if (currentVolume.value < 60) return '#FF9800'
      return '#F44336'
    })

    // 申请麦克风权限
    const requestPermission = async () => {
      isRequesting.value = true
      error.value = ''

      try {
        // 检查浏览器支持
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          throw new Error('浏览器不支持麦克风访问')
        }

        // 申请麦克风权限
        mediaStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
          },
        })

        hasPermission.value = true

        // 初始化音频上下文
        audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      } catch (err: any) {
        error.value = `权限申请失败: ${err.message}`
        hasPermission.value = false
      } finally {
        isRequesting.value = false
      }
    }

    // 开始/停止监听
    const toggleMonitoring = () => {
      if (isMonitoring.value) {
        stopMonitoring()
      } else {
        startMonitoring()
      }
    }

    // 开始监听
    const startMonitoring = () => {
      if (!audioContext || !mediaStream) {
        error.value = '音频上下文或媒体流未初始化'
        return
      }

      try {
        // 创建音频源节点
        sourceNode = audioContext.createMediaStreamSource(mediaStream)

        // 创建分析器节点
        analyserNode = audioContext.createAnalyser()
        analyserNode.fftSize = 256
        analyserNode.smoothingTimeConstant = 0.3

        // 连接节点
        sourceNode.connect(analyserNode)

        isMonitoring.value = true
        error.value = ''

        // 开始音量检测循环
        detectVolume()
      } catch (err: any) {
        error.value = `监听启动失败: ${err.message}`
      }
    }

    // 停止监听
    const stopMonitoring = () => {
      isMonitoring.value = false
      currentVolume.value = 0

      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
        animationFrame = null
      }

      if (sourceNode) {
        sourceNode.disconnect()
        sourceNode = null
      }

      analyserNode = null
    }

    // 音量检测
    const detectVolume = () => {
      if (!analyserNode || !isMonitoring.value) return

      const bufferLength = analyserNode.frequencyBinCount
      const dataArray = new Uint8Array(bufferLength)

      analyserNode.getByteFrequencyData(dataArray)

      // 计算平均音量
      let sum = 0
      for (let i = 0; i < bufferLength; i++) {
        sum += dataArray[i]
      }

      const average = sum / bufferLength
      currentVolume.value = (average / 255) * 100

      // 继续检测
      animationFrame = requestAnimationFrame(detectVolume)
    }

    // 清理资源
    const cleanup = () => {
      stopMonitoring()

      if (mediaStream) {
        mediaStream.getTracks().forEach((track) => track.stop())
        mediaStream = null
      }

      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close()
        audioContext = null
      }
    }

    // 组件卸载时清理
    onUnmounted(() => {
      cleanup()
    })
  },
})
</script>

<style scoped>
.microphone-monitor {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f8f9fa;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.monitor-header {
  text-align: center;
  margin-bottom: 2rem;
}

.monitor-header h2 {
  font-size: 2rem;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.description {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

.control-panel {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.permission-btn,
.monitor-btn {
  padding: 12px 24px;
  font-size: 1rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.permission-btn {
  background: #007bff;
  color: white;
}

.permission-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-2px);
}

.permission-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.monitor-btn {
  background: #28a745;
  color: white;
}

.monitor-btn.active {
  background: #dc3545;
}

.monitor-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.volume-display {
  margin-bottom: 2rem;
}

.volume-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.volume-value {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.label {
  color: #666;
  font-weight: 500;
}

.value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
}

.volume-status {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  background: #e9ecef;
  color: #666;
  transition: all 0.3s ease;
}

.volume-status.active {
  background: #d4edda;
  color: #155724;
}

.volume-visualizer {
  display: flex;
  gap: 2rem;
  align-items: center;
  justify-content: center;
}

.volume-bar-container {
  flex: 1;
  max-width: 400px;
}

.volume-bar {
  width: 100%;
  height: 20px;
  background: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.volume-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50 0%, #ff9800 60%, #f44336 100%);
  transition: width 0.1s ease;
  border-radius: 10px;
}

.volume-scale {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: #666;
}

.volume-circle {
  flex-shrink: 0;
}

.volume-circle circle {
  transition: stroke-dashoffset 0.1s ease;
}

.status-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-label {
  color: #666;
  font-weight: 500;
}

.status-value {
  font-weight: 600;
}

.status-value.success {
  color: #28a745;
}

.status-value.error {
  color: #dc3545;
}

.status-value.inactive {
  color: #6c757d;
}

.error-message {
  padding: 1rem;
  background: #f8d7da;
  color: #721c24;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
}

@media (max-width: 768px) {
  .microphone-monitor {
    padding: 1rem;
  }

  .volume-visualizer {
    flex-direction: column;
    gap: 1rem;
  }

  .status-info {
    grid-template-columns: 1fr;
  }
}
</style>
