<script setup lang="ts"></script>

<template>
  <div class="app">
    <header>
      <div class="wrapper">
        <h1 class="app-title">🎤 麦克风音量监听器</h1>
        <p class="app-subtitle">实时获取麦克风数据并监听音量大小</p>
        <button>开启麦克风</button>
      </div>
    </header>

    <main class="main-content"></main>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
  padding: 2rem 0;
  margin-bottom: 2rem;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.app-title {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-subtitle {
  font-size: 1.2rem;
  margin: 0;
  opacity: 0.9;
}

.main-content {
  padding: 0 2rem 2rem;
}

@media (max-width: 768px) {
  .wrapper {
    padding: 0 1rem;
  }

  .app-title {
    font-size: 2.2rem;
  }

  .app-subtitle {
    font-size: 1rem;
  }

  .main-content {
    padding: 0 1rem 2rem;
  }
}
</style>
