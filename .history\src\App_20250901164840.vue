<script setup lang="ts">
import { ref, onUnmounted } from 'vue'
import { AudioProcessor } from './utils/AudioProcessor'
import { WebSocketManager } from './utils/WebSocketManager'
import { AudioPlaybackManager } from './utils/AudioPlaybackManager'

const micPermissionStatus = ref<string>('未知')
const isRequestingPermission = ref<boolean>(false)
const isMonitoring = ref<boolean>(false)
const currentVolume = ref<number>(0)
const maxVolume = ref<number>(0)
const audioStream = ref<MediaStream | null>(null)
const audioContext = ref<AudioContext | null>(null)
const analyser = ref<AnalyserNode | null>(null)
const animationId = ref<number | null>(null)

// 新增：音频数据格式信息
const audioInfo = ref<any>({})

const getStatusClass = () => {
  switch (micPermissionStatus.value) {
    case '已授权':
      return 'status-granted'
    case '用户拒绝':
      return 'status-denied'
    case '未找到麦克风设备':
      return 'status-error'
    case '获取失败':
      return 'status-error'
    default:
      return 'status-unknown'
  }
}

const requestMicrophonePermission = async () => {
  isRequestingPermission.value = true

  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    micPermissionStatus.value = '已授权'
    audioStream.value = stream

    // 打印音频流信息
    console.log('=== 音频流信息 ===')
    console.log('MediaStream:', stream)
    console.log('音频轨道数量:', stream.getAudioTracks().length)
    
    const audioTrack = stream.getAudioTracks()[0]
    if (audioTrack) {
      console.log('音频轨道设置:', audioTrack.getSettings())
      console.log('音频轨道约束:', audioTrack.getConstraints())
      console.log('音频轨道能力:', audioTrack.getCapabilities())
    }

    console.log('麦克风权限获取成功')
  } catch (error) {
    console.error('麦克风权限获取失败:', error)

    if (error instanceof Error) {
      if (error.name === 'NotAllowedError') {
        micPermissionStatus.value = '用户拒绝'
      } else if (error.name === 'NotFoundError') {
        micPermissionStatus.value = '未找到麦克风设备'
      } else {
        micPermissionStatus.value = '获取失败'
      }
    } else {
      micPermissionStatus.value = '获取失败'
    }
  } finally {
    isRequestingPermission.value = false
  }
}

// 在 startMonitoring 函数中修改以下配置
const startMonitoring = async () => {
  if (!audioStream.value) {
    await requestMicrophonePermission()
    if (!audioStream.value) return
  }

  try {
    // 创建音频上下文
    audioContext.value = new (window.AudioContext || (window as any).webkitAudioContext)()
    
    // 创建分析器节点 - 提升质量配置
    analyser.value = audioContext.value.createAnalyser()
    
    // 🔧 高质量配置
    analyser.value.fftSize = 2048        // 提升到2048，更高的频率分辨率
    analyser.value.smoothingTimeConstant = 0.3  // 降低平滑，提高响应性
    analyser.value.minDecibels = -90     // 扩大动态范围
    analyser.value.maxDecibels = -10     // 扩大动态范围
    
    // 打印音频上下文信息
    console.log('=== 音频上下文信息 ===')
    console.log('采样率:', audioContext.value.sampleRate, 'Hz')
    console.log('音频上下文状态:', audioContext.value.state)
    console.log('基础延迟:', audioContext.value.baseLatency)
    console.log('输出延迟:', audioContext.value.outputLatency)
    
    // 创建分析器节点
    analyser.value = audioContext.value.createAnalyser()
    analyser.value.fftSize = 256
    analyser.value.smoothingTimeConstant = 0.8
    
    // 打印分析器信息
    console.log('=== 分析器节点信息 ===')
    console.log('FFT 大小:', analyser.value.fftSize)
    console.log('频率数据长度:', analyser.value.frequencyBinCount)
    console.log('平滑时间常数:', analyser.value.smoothingTimeConstant)
    console.log('最小分贝值:', analyser.value.minDecibels)
    console.log('最大分贝值:', analyser.value.maxDecibels)
    
    // 连接音频源
    const source = audioContext.value.createMediaStreamSource(audioStream.value)
    source.connect(analyser.value)
    
    // 保存音频信息
    audioInfo.value = {
      sampleRate: audioContext.value.sampleRate,
      fftSize: analyser.value.fftSize,
      frequencyBinCount: analyser.value.frequencyBinCount,
      smoothingTimeConstant: analyser.value.smoothingTimeConstant
    }
    
    isMonitoring.value = true
    monitorVolume()
    
    console.log('开始监听音量')
  } catch (error) {
    console.error('启动音量监听失败:', error)
  }
}

const stopMonitoring = () => {
  isMonitoring.value = false
  
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
    animationId.value = null
  }
  
  if (audioContext.value) {
    audioContext.value.close()
    audioContext.value = null
  }
  
  if (audioStream.value) {
    audioStream.value.getTracks().forEach(track => track.stop())
    audioStream.value = null
  }
  
  analyser.value = null
  currentVolume.value = 0
  maxVolume.value = 0
  audioInfo.value = {}
  
  console.log('停止监听音量')
}

// 在 monitorVolume 函数中使用更高精度的数据
const monitorVolume = () => {
  if (!analyser.value || !isMonitoring.value) return
  
  const bufferLength = analyser.value.frequencyBinCount
  // 🔧 使用Float32Array获得更高精度的频域数据
  const frequencyData = new Float32Array(bufferLength)  // 改为Float32
  const timeData = new Float32Array(analyser.value.fftSize)
  
  const updateVolume = () => {
    if (!analyser.value || !isMonitoring.value) return
    
    // 🔧 获取高精度频域数据 (-Infinity 到 0 dB)
    analyser.value.getFloatFrequencyData(frequencyData)
    analyser.value.getFloatTimeDomainData(timeData)
    
    // 每秒打印一次详细的音频数据信息
    if (Math.random() < 0.016) {
      console.log('=== 高质量音频数据详情 ===')
      console.log('时间戳:', new Date().toISOString())
      
      // 高精度频域数据信息
      console.log('频域数据类型:', frequencyData.constructor.name)
      console.log('频域数据长度:', frequencyData.length)
      console.log('频域数据范围: -Infinity 到 0 dB (Float32)')
      console.log('频域数据前10个值 (dB):', Array.from(frequencyData.slice(0, 10)))
      
      // 时域数据信息
      console.log('时域数据类型:', timeData.constructor.name)
      console.log('时域数据长度:', timeData.length)
      console.log('时域数据范围: -1.0 到 1.0 (Float32)')
      console.log('时域数据前10个值:', Array.from(timeData.slice(0, 10)))
      
      // 高质量音频格式总结
      console.log('=== 高质量音频格式总结 ===')
      console.log('采样率:', audioInfo.value.sampleRate, 'Hz')
      console.log('FFT大小:', audioInfo.value.fftSize)
      console.log('频率分辨率:', (audioInfo.value.sampleRate / audioInfo.value.fftSize).toFixed(1), 'Hz/bin')
      console.log('动态范围: -90dB 到 -10dB')
      console.log('数据精度: 32位浮点 (频域和时域)')
      
      // 计算高精度统计信息
      const validFreqData = frequencyData.filter(val => val > -Infinity)
      const frequencyAvg = validFreqData.reduce((sum, val) => sum + val, 0) / validFreqData.length
      const timeRMS = Math.sqrt(timeData.reduce((sum, val) => sum + val * val, 0) / timeData.length)
      
      console.log('频域平均值 (dB):', frequencyAvg.toFixed(2))
      console.log('时域RMS值:', timeRMS.toFixed(6))
      console.log('有效频率数据点:', validFreqData.length, '/', frequencyData.length)
      console.log('========================')
    }
    
    // 🔧 使用高精度数据计算音量
    const validData = frequencyData.filter(val => val > -Infinity)
    const avgDb = validData.reduce((sum, val) => sum + val, 0) / validData.length
    
    // 将dB值转换为线性比例 (0-100)
    const minDb = -90
    const maxDb = -10
    const normalizedDb = Math.max(0, Math.min(1, (avgDb - minDb) / (maxDb - minDb)))
    const volumePercent = Math.round(normalizedDb * 100)
    
    currentVolume.value = volumePercent
    
    // 更新最大音量
    if (volumePercent > maxVolume.value) {
      maxVolume.value = volumePercent
    }
    
    animationId.value = requestAnimationFrame(updateVolume)
  }
  
  updateVolume()
}

const resetMaxVolume = () => {
  maxVolume.value = 0
}

const getVolumeBarStyle = () => {
  return {
    width: `${currentVolume.value}%`,
    backgroundColor: getVolumeColor(currentVolume.value)
  }
}

const getVolumeColor = (volume: number) => {
  if (volume < 20) return '#10b981' // 绿色
  if (volume < 50) return '#f59e0b' // 黄色
  if (volume < 80) return '#f97316' // 橙色
  return '#ef4444' // 红色
}

// 新增：手动打印当前音频数据
const printCurrentAudioData = () => {
  if (!analyser.value) {
    console.log('分析器未初始化，请先开始监听')
    return
  }
  
  const bufferLength = analyser.value.frequencyBinCount
  const frequencyData = new Uint8Array(bufferLength)
  const timeData = new Float32Array(analyser.value.fftSize)
  
  analyser.value.getByteFrequencyData(frequencyData)
  analyser.value.getFloatTimeDomainData(timeData)
  
  console.log('=== 手动获取的音频数据 ===')
  console.log('完整频域数据:', frequencyData)
  console.log('完整时域数据:', timeData)
  console.log('频域数据字节长度:', frequencyData.byteLength)
  console.log('时域数据字节长度:', timeData.byteLength)
}

// 组件卸载时清理资源
onUnmounted(() => {
  stopMonitoring()
})
</script>

<template>
  <div class="app">
    <header>
      <div class="wrapper">
        <h1 class="app-title">🎤 麦克风音量监听器</h1>
        <p class="app-subtitle">实时获取麦克风数据并监听音量大小</p>
      </div>
    </header>

    <main class="main-content">
      <div class="permission-section">
        <div class="permission-card">
          <h2>麦克风权限</h2>
          <p class="permission-status">
            当前状态: <span :class="getStatusClass()">{{ micPermissionStatus }}</span>
          </p>
          <button
            @click="requestMicrophonePermission"
            :disabled="isRequestingPermission"
            class="permission-btn"
          >
            {{ isRequestingPermission ? '请求中...' : '获取麦克风权限' }}
          </button>
        </div>
      </div>

      <!-- 音量监听区域 -->
      <div class="volume-section" v-if="micPermissionStatus === '已授权'">
        <div class="volume-card">
          <h2>音量监听</h2>
          
          <!-- 控制按钮 -->
          <div class="control-buttons">
            <button
              @click="startMonitoring"
              :disabled="isMonitoring"
              class="control-btn start-btn"
            >
              {{ isMonitoring ? '监听中...' : '开始监听' }}
            </button>
            <button
              @click="stopMonitoring"
              :disabled="!isMonitoring"
              class="control-btn stop-btn"
            >
              停止监听
            </button>
            <button
              @click="resetMaxVolume"
              :disabled="!isMonitoring"
              class="control-btn reset-btn"
            >
              重置最大值
            </button>
            <button
              @click="printCurrentAudioData"
              :disabled="!isMonitoring"
              class="control-btn debug-btn"
            >
              打印音频数据
            </button>
          </div>

          <!-- 音频信息显示 -->
          <div class="audio-info" v-if="isMonitoring">
            <h3>音频格式信息</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">采样率:</span>
                <span class="info-value">{{ audioInfo.sampleRate }} Hz</span>
              </div>
              <div class="info-item">
                <span class="info-label">FFT大小:</span>
                <span class="info-value">{{ audioInfo.fftSize }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">频率分辨率:</span>
                <span class="info-value">{{ (audioInfo.sampleRate / audioInfo.fftSize).toFixed(1) }} Hz/bin</span>
              </div>
              <div class="info-item">
                <span class="info-label">频域数据长度:</span>
                <span class="info-value">{{ audioInfo.frequencyBinCount }}</span>
              </div>
            </div>
          </div>

          <!-- 音量显示 -->
          <div class="volume-display" v-if="isMonitoring">
            <div class="volume-info">
              <div class="volume-item">
                <span class="volume-label">当前音量:</span>
                <span class="volume-value" :style="{ color: getVolumeColor(currentVolume) }">
                  {{ currentVolume }}%
                </span>
              </div>
              <div class="volume-item">
                <span class="volume-label">最大音量:</span>
                <span class="volume-value max-volume">
                  {{ maxVolume }}%
                </span>
              </div>
            </div>
            
            <!-- 音量条 -->
            <div class="volume-bar-container">
              <div class="volume-bar-bg">
                <div class="volume-bar" :style="getVolumeBarStyle()"></div>
              </div>
              <div class="volume-scale">
                <span>0%</span>
                <span>25%</span>
                <span>50%</span>
                <span>75%</span>
                <span>100%</span>
              </div>
            </div>
            
            <!-- 音量圆形指示器 -->
            <div class="volume-circle">
              <svg width="120" height="120" class="volume-svg">
                <circle
                  cx="60"
                  cy="60"
                  r="50"
                  fill="none"
                  stroke="#e5e7eb"
                  stroke-width="8"
                />
                <circle
                  cx="60"
                  cy="60"
                  r="50"
                  fill="none"
                  :stroke="getVolumeColor(currentVolume)"
                  stroke-width="8"
                  stroke-linecap="round"
                  :stroke-dasharray="`${(currentVolume / 100) * 314} 314`"
                  transform="rotate(-90 60 60)"
                  class="volume-progress"
                />
              </svg>
              <div class="volume-text">
                <span class="volume-number">{{ currentVolume }}</span>
                <span class="volume-percent">%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
  padding: 2rem 0;
  margin-bottom: 2rem;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.app-title {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-subtitle {
  font-size: 1.2rem;
  margin: 0;
  opacity: 0.9;
}

.main-content {
  padding: 0 2rem 2rem;
}

.permission-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  margin-bottom: 2rem;
}

.permission-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.permission-card h2 {
  color: #333;
  font-size: 2rem;
  margin: 0 0 1.5rem 0;
  font-weight: 600;
}

.permission-status {
  color: #666;
  font-size: 1.1rem;
  margin: 0 0 2rem 0;
}

.status-granted {
  color: #10b981;
  font-weight: 600;
}

.status-denied {
  color: #ef4444;
  font-weight: 600;
}

.status-error {
  color: #f59e0b;
  font-weight: 600;
}

.status-unknown {
  color: #6b7280;
  font-weight: 600;
}

.permission-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.permission-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.permission-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 音量监听区域样式 */
.volume-section {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.volume-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  text-align: center;
  max-width: 600px;
  width: 100%;
}

.volume-card h2 {
  color: #333;
  font-size: 2rem;
  margin: 0 0 2rem 0;
  font-weight: 600;
}

.control-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.control-btn {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.start-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.start-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.6);
}

.stop-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.stop-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.6);
}

.reset-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
}

.reset-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.6);
}

.control-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.volume-display {
  margin-top: 2rem;
}

.volume-info {
  display: flex;
  justify-content: space-around;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.volume-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.volume-label {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.volume-value {
  font-size: 1.5rem;
  font-weight: 700;
}

.max-volume {
  color: #8b5cf6;
}

.volume-bar-container {
  margin: 2rem 0;
}

.volume-bar-bg {
  width: 100%;
  height: 20px;
  background: #e5e7eb;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.volume-bar {
  height: 100%;
  transition: width 0.1s ease;
  border-radius: 10px;
}

.volume-scale {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #666;
}

.volume-circle {
  position: relative;
  display: inline-block;
  margin-top: 2rem;
}

.volume-svg {
  transform: rotate(-90deg);
}

.volume-progress {
  transition: stroke-dasharray 0.1s ease;
}

.volume-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.volume-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
}

.volume-percent {
  font-size: 1rem;
  color: #666;
}

@media (max-width: 768px) {
  .wrapper {
    padding: 0 1rem;
  }

  .app-title {
    font-size: 2.2rem;
  }

  .app-subtitle {
    font-size: 1rem;
  }

  .main-content {
    padding: 0 1rem 2rem;
  }

  .permission-card,
  .volume-card {
    padding: 2rem;
    margin: 0 1rem;
  }

  .permission-card h2,
  .volume-card h2 {
    font-size: 1.5rem;
  }

  .control-buttons {
    flex-direction: column;
    align-items: center;
  }

  .control-btn {
    width: 200px;
  }

  .volume-info {
    flex-direction: column;
    gap: 1.5rem;
  }
}
</style>

## 🎵 音频数据WebSocket传输与播放完整方案

基于您现有的项目架构，我来为您提供完整的实现方案：

### 1. 📤 发送音频数据到后端（PCM格式）

首先修改 <mcfile name="App.vue" path="/Users/<USER>/Desktop/industry_trade/rtcdemo/src/App.vue"></mcfile>，集成WebSocket和音频处理：
```vue
<script setup lang="ts">
import { ref, onUnmounted } from 'vue'
import { AudioProcessor } from './utils/AudioProcessor'
import { WebSocketManager } from './utils/WebSocketManager'
import { AudioPlaybackManager } from './utils/AudioPlaybackManager'

// ... existing code ...

// 新增：音频处理和WebSocket管理
const audioProcessor = ref<AudioProcessor | null>(null)
const wsManager = ref<WebSocketManager | null>(null)
const playbackManager = ref<AudioPlaybackManager | null>(null)
const isConnected = ref(false)
const isTransmitting = ref(false)

// 初始化WebSocket连接
const initWebSocket = async () => {
  try {
    wsManager.value = new WebSocketManager({
      url: 'ws://localhost:8080/audio', // 替换为您的WebSocket服务地址
      binaryType: 'arraybuffer'
    })
    
    // 监听连接状态
    wsManager.value.onStateChange((status) => {
      isConnected.value = status.state === 'connected'
      console.log('WebSocket状态:', status)
    })
    
    // 监听接收到的音频数据
    wsManager.value.onAudioData((audioData) => {
      playReceivedAudio(audioData)
    })
    
    await wsManager.value.connect()
    console.log('WebSocket连接成功')
  } catch (error) {
    console.error('WebSocket连接失败:', error)
  }
}

// 开始音频传输
const startAudioTransmission = async () => {
  if (!wsManager.value || !isConnected.value) {
    await initWebSocket()
  }
  
  // 初始化音频处理器
  audioProcessor.value = new AudioProcessor({
    sampleRate: 16000,
    channels: 1,
    bufferSize: 4096
  })
  
  // 设置音频数据回调
  audioProcessor.value.setAudioDataCallback((audioData: Float32Array) => {
    // 转换为PCM格式
    const pcmData = audioProcessor.value!.convertToPCM(audioData)
    
    // 通过WebSocket发送PCM数据
    if (wsManager.value && isConnected.value) {
      wsManager.value.sendAudioData(pcmData)
    }
  })
  
  await audioProcessor.value.startRecording()
  isTransmitting.value = true
  console.log('开始音频传输')
}

// 停止音频传输
const stopAudioTransmission = () => {
  if (audioProcessor.value) {
    audioProcessor.value.stopRecording()
    audioProcessor.value = null
  }
  isTransmitting.value = false
  console.log('停止音频传输')
}

// 播放接收到的音频数据
const playReceivedAudio = async (audioData: ArrayBuffer) => {
  if (!playbackManager.value) {
    playbackManager.value = new AudioPlaybackManager()
    await playbackManager.value.initAudioContext()
  }
  
  try {
    await playbackManager.value.playAudio(audioData, {
      volume: 1.0,
      canBeInterrupted: true
    })
  } catch (error) {
    console.error('音频播放失败:', error)
  }
}

// 组件卸载时清理资源
onUnmounted(() => {
  stopMonitoring()
  stopAudioTransmission()
  if (wsManager.value) {
    wsManager.value.destroy()
  }
  if (playbackManager.value) {
    playbackManager.value.destroy()
  }
})
</script>

<template>
  <div class="app">
    <header>
      <div class="wrapper">
        <h1 class="app-title">🎤 麦克风音量监听器</h1>
        <p class="app-subtitle">实时获取麦克风数据并监听音量大小</p>
      </div>
    </header>

    <main class="main-content">
      <div class="permission-section">
        <div class="permission-card">
          <h2>麦克风权限</h2>
          <p class="permission-status">
            当前状态: <span :class="getStatusClass()">{{ micPermissionStatus }}</span>
          </p>
          <button
            @click="requestMicrophonePermission"
            :disabled="isRequestingPermission"
            class="permission-btn"
          >
            {{ isRequestingPermission ? '请求中...' : '获取麦克风权限' }}
          </button>
        </div>
      </div>

      <!-- 音量监听区域 -->
      <div class="volume-section" v-if="micPermissionStatus === '已授权'">
        <div class="volume-card">
          <h2>音量监听</h2>
          
          <!-- 控制按钮 -->
          <div class="control-buttons">
            <button
              @click="startMonitoring"
              :disabled="isMonitoring"
              class="control-btn start-btn"
            >
              {{ isMonitoring ? '监听中...' : '开始监听' }}
            </button>
            <button
              @click="stopMonitoring"
              :disabled="!isMonitoring"
              class="control-btn stop-btn"
            >
              停止监听
            </button>
            <button
              @click="resetMaxVolume"
              :disabled="!isMonitoring"
              class="control-btn reset-btn"
            >
              重置最大值
            </button>
            <button
              @click="printCurrentAudioData"
              :disabled="!isMonitoring"
              class="control-btn debug-btn"
            >
              打印音频数据
            </button>
          </div>

          <!-- 音频信息显示 -->
          <div class="audio-info" v-if="isMonitoring">
            <h3>音频格式信息</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">采样率:</span>
                <span class="info-value">{{ audioInfo.sampleRate }} Hz</span>
              </div>
              <div class="info-item">
                <span class="info-label">FFT大小:</span>
                <span class="info-value">{{ audioInfo.fftSize }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">频率分辨率:</span>
                <span class="info-value">{{ (audioInfo.sampleRate / audioInfo.fftSize).toFixed(1) }} Hz/bin</span>
              </div>
              <div class="info-item">
                <span class="info-label">频域数据长度:</span>
                <span class="info-value">{{ audioInfo.frequencyBinCount }}</span>
              </div>
            </div>
          </div>

          <!-- 音量显示 -->
          <div class="volume-display" v-if="isMonitoring">
            <div class="volume-info">
              <div class="volume-item">
                <span class="volume-label">当前音量:</span>
                <span class="volume-value" :style="{ color: getVolumeColor(currentVolume) }">
                  {{ currentVolume }}%
                </span>
              </div>
              <div class="volume-item">
                <span class="volume-label">最大音量:</span>
                <span class="volume-value max-volume">
                  {{ maxVolume }}%
                </span>
              </div>
            </div>
            
            <!-- 音量条 -->
            <div class="volume-bar-container">
              <div class="volume-bar-bg">
                <div class="volume-bar" :style="getVolumeBarStyle()"></div>
              </div>
              <div class="volume-scale">
                <span>0%</span>
                <span>25%</span>
                <span>50%</span>
                <span>75%</span>
                <span>100%</span>
              </div>
            </div>
            
            <!-- 音量圆形指示器 -->
            <div class="volume-circle">
              <svg width="120" height="120" class="volume-svg">
                <circle
                  cx="60"
                  cy="60"
                  r="50"
                  fill="none"
                  stroke="#e5e7eb"
                  stroke-width="8"
                />
                <circle
                  cx="60"
                  cy="60"
                  r="50"
                  fill="none"
                  :stroke="getVolumeColor(currentVolume)"
                  stroke-width="8"
                  stroke-linecap="round"
                  :stroke-dasharray="`${(currentVolume / 100) * 314} 314`"
                  transform="rotate(-90 60 60)"
                  class="volume-progress"
                />
              </svg>
              <div class="volume-text">
                <span class="volume-number">{{ currentVolume }}</span>
                <span class="volume-percent">%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
  padding: 2rem 0;
  margin-bottom: 2rem;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.app-title {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-subtitle {
  font-size: 1.2rem;
  margin: 0;
  opacity: 0.9;
}

.main-content {
  padding: 0 2rem 2rem;
}

.permission-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  margin-bottom: 2rem;
}

.permission-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.permission-card h2 {
  color: #333;
  font-size: 2rem;
  margin: 0 0 1.5rem 0;
  font-weight: 600;
}

.permission-status {
  color: #666;
  font-size: 1.1rem;
  margin: 0 0 2rem 0;
}

.status-granted {
  color: #10b981;
  font-weight: 600;
}

.status-denied {
  color: #ef4444;
  font-weight: 600;
}

.status-error {
  color: #f59e0b;
  font-weight: 600;
}

.status-unknown {
  color: #6b7280;
  font-weight: 600;
}

.permission-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.permission-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.permission-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 音量监听区域样式 */
.volume-section {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.volume-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  text-align: center;
  max-width: 600px;
  width: 100%;
}

.volume-card h2 {
  color: #333;
  font-size: 2rem;
  margin: 0 0 2rem 0;
  font-weight: 600;
}

.control-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.control-btn {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.start-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.start-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.6);
}

.stop-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.stop-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.6);
}

.reset-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
}

.reset-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.6);
}

.control-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.volume-display {
  margin-top: 2rem;
}

.volume-info {
  display: flex;
  justify-content: space-around;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.volume-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.volume-label {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.volume-value {
  font-size: 1.5rem;
  font-weight: 700;
}

.max-volume {
  color: #8b5cf6;
}

.volume-bar-container {
  margin: 2rem 0;
}

.volume-bar-bg {
  width: 100%;
  height: 20px;
  background: #e5e7eb;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.volume-bar {
  height: 100%;
  transition: width 0.1s ease;
  border-radius: 10px;
}

.volume-scale {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #666;
}

.volume-circle {
  position: relative;
  display: inline-block;
  margin-top: 2rem;
}

.volume-svg {
  transform: rotate(-90deg);
}

.volume-progress {
  transition: stroke-dasharray 0.1s ease;
}

.volume-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.volume-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
}

.volume-percent {
  font-size: 1rem;
  color: #666;
}

@media (max-width: 768px) {
  .wrapper {
    padding: 0 1rem;
  }

  .app-title {
    font-size: 2.2rem;
  }

  .app-subtitle {
    font-size: 1rem;
  }

  .main-content {
    padding: 0 1rem 2rem;
  }

  .permission-card,
  .volume-card {
    padding: 2rem;
    margin: 0 1rem;
  }

  .permission-card h2,
  .volume-card h2 {
    font-size: 1.5rem;
  }

  .control-buttons {
    flex-direction: column;
    align-items: center;
  }

  .control-btn {
    width: 200px;
  }

  .volume-info {
    flex-direction: column;
    gap: 1.5rem;
  }
}
</style>
