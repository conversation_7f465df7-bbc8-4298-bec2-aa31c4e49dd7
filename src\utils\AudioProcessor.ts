/**
 * 音频处理工具类
 * 负责音频采集、音量检测、音频编码等核心功能
 */

export interface AudioConfig {
  sampleRate?: number
  channels?: number
  bufferSize?: number
  volumeThreshold?: number // 音量阈值，超过此值触发打断
}

export interface AudioVolume {
  volume: number // 0-100的音量值
  isAboveThreshold: boolean // 是否超过阈值
}

export class AudioProcessor {
  private audioContext: AudioContext | null = null
  private mediaStream: MediaStream | null = null
  private sourceNode: MediaStreamAudioSourceNode | null = null
  private analyserNode: AnalyserNode | null = null
  private processorNode: ScriptProcessorNode | null = null
  private isRecording = false
  private config: Required<AudioConfig>

  // 音量监听相关
  private volumeCallback?: (volume: AudioVolume) => void
  private volumeCheckInterval?: number

  // 音频数据回调
  private audioDataCallback?: (audioData: Float32Array) => void

  constructor(config: AudioConfig = {}) {
    this.config = {
      sampleRate: config.sampleRate || 16000,
      channels: config.channels || 1,
      bufferSize: config.bufferSize || 4096,
      volumeThreshold: config.volumeThreshold || 30,
    }
  }

  /**
   * 初始化音频上下文
   */
  async initAudioContext(): Promise<void> {
    try {
      // 创建音频上下文
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: this.config.sampleRate,
      })

      // 如果音频上下文被暂停，则恢复
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }
    } catch (error) {
      throw new Error(`音频上下文初始化失败: ${error}`)
    }
  }

  /**
   * 请求麦克风权限并创建媒体流
   */
  async requestMicrophone(): Promise<void> {
    try {
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.config.sampleRate,
          channelCount: this.config.channels,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      })
    } catch (error) {
      throw new Error(`麦克风权限获取失败: ${error}`)
    }
  }

  /**
   * 开始录音
   */
  async startRecording(): Promise<void> {
    if (this.isRecording) {
      console.warn('录音已经在进行中')
      return
    }

    try {
      // 初始化音频上下文
      if (!this.audioContext) {
        await this.initAudioContext()
      }

      // 请求麦克风权限
      if (!this.mediaStream) {
        await this.requestMicrophone()
      }

      if (!this.audioContext || !this.mediaStream) {
        throw new Error('音频上下文或媒体流未初始化')
      }

      // 创建音频源节点
      this.sourceNode = this.audioContext.createMediaStreamSource(this.mediaStream)

      // 创建分析器节点（用于音量检测）
      this.analyserNode = this.audioContext.createAnalyser()
      this.analyserNode.fftSize = 256
      this.analyserNode.smoothingTimeConstant = 0.3

      // 创建脚本处理器节点（用于音频数据处理）
      this.processorNode = this.audioContext.createScriptProcessor(
        this.config.bufferSize,
        this.config.channels,
        this.config.channels,
      )

      // 连接音频节点
      this.sourceNode.connect(this.analyserNode)
      this.sourceNode.connect(this.processorNode)
      this.processorNode.connect(this.audioContext.destination)

      // 处理音频数据
      this.processorNode.onaudioprocess = (event) => {
        if (this.isRecording && this.audioDataCallback) {
          const inputBuffer = event.inputBuffer
          const channelData = inputBuffer.getChannelData(0)

          // 将音频数据传递给回调函数
          this.audioDataCallback(channelData)
        }
      }

      // 开始音量监听
      this.startVolumeMonitoring()

      this.isRecording = true
      console.log('录音已开始')
    } catch (error) {
      throw new Error(`录音启动失败: ${error}`)
    }
  }

  /**
   * 停止录音
   */
  stopRecording(): void {
    if (!this.isRecording) {
      console.warn('录音未在进行中')
      return
    }

    // 停止音量监听
    this.stopVolumeMonitoring()

    // 断开音频节点连接
    if (this.sourceNode) {
      this.sourceNode.disconnect()
      this.sourceNode = null
    }

    if (this.processorNode) {
      this.processorNode.disconnect()
      this.processorNode = null
    }

    if (this.analyserNode) {
      this.analyserNode = null
    }

    // 停止媒体流
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach((track) => track.stop())
      this.mediaStream = null
    }

    this.isRecording = false
    console.log('录音已停止')
  }

  /**
   * 开始音量监听
   */
  private startVolumeMonitoring(): void {
    if (!this.analyserNode) return

    const bufferLength = this.analyserNode.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)

    const checkVolume = () => {
      if (!this.analyserNode || !this.isRecording) return

      this.analyserNode.getByteFrequencyData(dataArray)

      // 计算音量（RMS值）
      let sum = 0
      for (let i = 0; i < bufferLength; i++) {
        sum += dataArray[i] * dataArray[i]
      }
      const rms = Math.sqrt(sum / bufferLength)
      const volume = Math.min(100, (rms / 255) * 100)

      const audioVolume: AudioVolume = {
        volume: Math.round(volume),
        isAboveThreshold: volume > this.config.volumeThreshold,
      }

      // 调用音量回调
      if (this.volumeCallback) {
        this.volumeCallback(audioVolume)
      }
    }

    // 每100ms检查一次音量
    this.volumeCheckInterval = window.setInterval(checkVolume, 100)
  }

  /**
   * 停止音量监听
   */
  private stopVolumeMonitoring(): void {
    if (this.volumeCheckInterval) {
      clearInterval(this.volumeCheckInterval)
      this.volumeCheckInterval = undefined
    }
  }

  /**
   * 设置音频数据回调
   */
  setAudioDataCallback(callback: (audioData: Float32Array) => void): void {
    this.audioDataCallback = callback
  }

  /**
   * 设置音量变化回调
   */
  setVolumeCallback(callback: (volume: AudioVolume) => void): void {
    this.volumeCallback = callback
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<AudioConfig>): void {
    this.config = { ...this.config, ...config }
  }

  /**
   * 音频数据转换为PCM格式
   */
  convertToPCM(audioData: Float32Array): ArrayBuffer {
    const buffer = new ArrayBuffer(audioData.length * 2)
    const view = new DataView(buffer)

    for (let i = 0; i < audioData.length; i++) {
      // 将浮点数转换为16位整数PCM
      const sample = Math.max(-1, Math.min(1, audioData[i]))
      view.setInt16(i * 2, sample * 0x7fff, true)
    }

    return buffer
  }

  /**
   * 播放音频数据
   */
  async playAudioBuffer(audioBuffer: ArrayBuffer): Promise<void> {
    if (!this.audioContext) {
      await this.initAudioContext()
    }

    if (!this.audioContext) {
      throw new Error('音频上下文未初始化')
    }

    try {
      // 解码音频数据
      const audioBufferSource = this.audioContext.createBufferSource()
      const decodedBuffer = await this.audioContext.decodeAudioData(audioBuffer)

      audioBufferSource.buffer = decodedBuffer
      audioBufferSource.connect(this.audioContext.destination)
      audioBufferSource.start()
    } catch (error) {
      console.error('音频播放失败:', error)
    }
  }

  /**
   * 检查浏览器支持
   */
  static checkBrowserSupport(): boolean {
    return !!(
      navigator.mediaDevices &&
      typeof navigator.mediaDevices.getUserMedia === 'function' &&
      (window.AudioContext || (window as any).webkitAudioContext)
    )
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.stopRecording()

    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close()
      this.audioContext = null
    }
  }

  /**
   * 获取分析器节点（用于可视化）
   */
  getAnalyserNode(): AnalyserNode | null {
    return this.analyserNode
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      isRecording: this.isRecording,
      hasAudioContext: !!this.audioContext,
      hasMediaStream: !!this.mediaStream,
      audioContextState: this.audioContext?.state,
      config: this.config,
    }
  }
}
