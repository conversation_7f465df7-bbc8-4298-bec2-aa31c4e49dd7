<script setup lang="ts">
import { ref } from 'vue'

const micPermissionStatus = ref<string>('未知')
const isRequestingPermission = ref<boolean>(false)

const requestMicrophonePermission = async () => {
  isRequestingPermission.value = true
  
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    micPermissionStatus.value = '已授权'
    
    // 获取权限后可以停止流，因为这里只是为了获取权限
    stream.getTracks().forEach(track => track.stop())
    
    console.log('麦克风权限获取成功')
  } catch (error) {
    console.error('麦克风权限获取失败:', error)
    
    if (error instanceof Error) {
      if (error.name === 'NotAllowedError') {
        micPermissionStatus.value = '用户拒绝'
      } else if (error.name === 'NotFoundError') {
        micPermissionStatus.value = '未找到麦克风设备'
      } else {
        micPermissionStatus.value = '获取失败'
      }
    } else {
      micPermissionStatus.value = '获取失败'
    }
  } finally {
    isRequestingPermission.value = false
  }
}
</script>
<template>
  <div class="app">
    <header>
      <div class="wrapper">
        <h1 class="app-title">🎤 麦克风音量监听器</h1>
        <p class="app-subtitle">实时获取麦克风数据并监听音量大小</p>
      </div>
    </header>

    <main class="main-content">
      <div class="permission-section">
        <div class="permission-card">
          <h2>麦克风权限</h2>
          <p class="permission-status">
            当前状态: <span :class="getStatusClass()">{{ micPermissionStatus }}</span>
          </p>
          <button 
            @click="requestMicrophonePermission" 
            :disabled="isRequestingPermission"
            class="permission-btn"
          >
            {{ isRequestingPermission ? '请求中...' : '获取麦克风权限' }}
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
const getStatusClass = () => {
  switch (micPermissionStatus.value) {
    case '已授权':
      return 'status-granted'
    case '用户拒绝':
      return 'status-denied'
    case '未找到麦克风设备':
      return 'status-error'
    case '获取失败':
      return 'status-error'
    default:
      return 'status-unknown'
  }
}
</script>

<style scoped>
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
  padding: 2rem 0;
  margin-bottom: 2rem;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.app-title {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-subtitle {
  font-size: 1.2rem;
  margin: 0;
  opacity: 0.9;
}

.main-content {
  padding: 0 2rem 2rem;
}

.permission-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.permission-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.permission-card h2 {
  color: #333;
  font-size: 2rem;
  margin: 0 0 1.5rem 0;
  font-weight: 600;
}

.permission-status {
  color: #666;
  font-size: 1.1rem;
  margin: 0 0 2rem 0;
}

.status-granted {
  color: #10b981;
  font-weight: 600;
}

.status-denied {
  color: #ef4444;
  font-weight: 600;
}

.status-error {
  color: #f59e0b;
  font-weight: 600;
}

.status-unknown {
  color: #6b7280;
  font-weight: 600;
}

.permission-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.permission-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.permission-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@media (max-width: 768px) {
  .wrapper {
    padding: 0 1rem;
  }

  .app-title {
    font-size: 2.2rem;
  }

  .app-subtitle {
    font-size: 1rem;
  }

  .main-content {
    padding: 0 1rem 2rem;
  }

  .permission-card {
    padding: 2rem;
    margin: 0 1rem;
  }

  .permission-card h2 {
    font-size: 1.5rem;
  }
}
</style>
