/**
 * WebSocket连接管理器
 * 负责处理实时音频流传输和连接状态管理
 */

export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

export interface WebSocketConfig {
  url: string
  protocols?: string[]
  reconnectInterval?: number // 重连间隔（毫秒）
  maxReconnectAttempts?: number // 最大重连次数
  heartbeatInterval?: number // 心跳间隔（毫秒）
  binaryType?: BinaryType // 二进制数据类型
}

// 根据示例修改消息接口
export interface ServerMessage {
  type: 'tts_audio' | 'server_event' | 'error' | 'session_started'
  data?: string // base64编码的音频数据
  event?: string // 服务器事件描述
  message?: string // 错误消息
}

export interface ClientMessage {
  type: 'start_session' | 'audio_data'
  data?: string // base64编码的音频数据
}

export interface ConnectionStatus {
  state: ConnectionState
  reconnectAttempts: number
  lastError?: string
  latency?: number
}

export class WebSocketManager {
  private websocket: WebSocket | null = null
  private config: Required<WebSocketConfig>
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED
  private reconnectAttempts = 0
  private reconnectTimer?: number
  private heartbeatTimer?: number
  private lastHeartbeatTime = 0
  private audioContext: AudioContext | null = null

  // 事件回调
  private onStateChangeCallback?: (status: ConnectionStatus) => void
  private onAudioDataCallback?: (audioData: ArrayBuffer) => void
  private onServerEventCallback?: (event: string) => void
  private onErrorCallback?: (error: string) => void

  constructor(config: WebSocketConfig) {
    this.config = {
      url: config.url,
      protocols: config.protocols || [],
      reconnectInterval: config.reconnectInterval || 3000,
      maxReconnectAttempts: config.maxReconnectAttempts || 10,
      heartbeatInterval: config.heartbeatInterval || 30000,
      binaryType: config.binaryType || 'arraybuffer',
    }

    // 初始化音频上下文
    this.audioContext = new AudioContext()
  }

  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      console.warn('WebSocket已经连接')
      return
    }

    this.updateConnectionState(ConnectionState.CONNECTING)

    try {
      // 创建WebSocket连接
      this.websocket = new WebSocket(this.config.url, this.config.protocols)
      this.websocket.binaryType = this.config.binaryType

      // 设置事件监听器
      this.setupEventListeners()

      // 等待连接建立
      await this.waitForConnection()

      console.log('WebSocket连接成功')
    } catch (error) {
      this.handleConnectionError(error as Error)
      throw error
    }
  }

  /**
   * 等待连接建立
   */
  private waitForConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.websocket) {
        reject(new Error('WebSocket未初始化'))
        return
      }

      const timeout = setTimeout(() => {
        reject(new Error('连接超时'))
      }, 10000)

      this.websocket.onopen = () => {
        clearTimeout(timeout)
        this.updateConnectionState(ConnectionState.CONNECTED)
        this.reconnectAttempts = 0
        console.log('WebSocket连接已建立')
        // 自动开始会话
        this.startSession()
        resolve()
      }

      this.websocket.onerror = (error) => {
        clearTimeout(timeout)
        reject(error)
      }
    })
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.websocket) return

    this.websocket.onopen = () => {
      this.updateConnectionState(ConnectionState.CONNECTED)
      this.reconnectAttempts = 0
      console.log('WebSocket连接已建立')
      // 自动开始会话
      this.startSession()
    }

    this.websocket.onclose = (event) => {
      console.log('WebSocket连接已关闭:', event.reason)
      this.updateConnectionState(ConnectionState.DISCONNECTED)

      // 如果不是主动关闭，则尝试重连
      if (!event.wasClean && this.shouldReconnect()) {
        this.scheduleReconnect()
      }
    }

    this.websocket.onerror = (error) => {
      console.error('WebSocket错误:', error)
      this.handleConnectionError(new Error('WebSocket连接错误'))
    }

    this.websocket.onmessage = (event) => {
      this.handleMessage(event)
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(event: MessageEvent): void {
    try {
      // 根据示例，所有消息都是JSON格式
      const data: ServerMessage = JSON.parse(event.data)
      this.handleServerMessage(data)
    } catch (error) {
      console.error('消息处理失败:', error)
    }
  }

  /**
   * 处理服务器消息
   */
  private handleServerMessage(data: ServerMessage): void {
    switch (data.type) {
      case 'tts_audio':
        if (data.data) {
          this.playAudio(data.data)
        }
        break
      case 'server_event':
        console.log('服务器事件:', data.event)
        if (this.onServerEventCallback && data.event) {
          this.onServerEventCallback(data.event)
        }
        break
      case 'error':
        console.error('错误:', data.message)
        if (this.onErrorCallback && data.message) {
          this.onErrorCallback(data.message)
        }
        break
      case 'session_started':
        console.log('会话已开始')
        break
      default:
        console.warn('未知消息类型:', data.type)
    }
  }

  /**
   * 开始会话
   */
  startSession(): void {
    if (!this.isConnected()) {
      console.warn('WebSocket未连接，无法开始会话')
      return
    }

    const message: ClientMessage = {
      type: 'start_session',
    }

    try {
      this.websocket!.send(JSON.stringify(message))
      console.log('已发送开始会话请求')
    } catch (error) {
      console.error('发送会话开始消息失败:', error)
    }
  }

  /**
   * 发送音频数据
   */
  sendAudioData(audioBuffer: ArrayBuffer): void {
    if (!this.isConnected()) {
      console.warn('WebSocket未连接，无法发送音频数据')
      return
    }

    try {
      // 转换为base64格式
      const base64Audio = this.arrayBufferToBase64(audioBuffer)

      const message: ClientMessage = {
        type: 'audio_data',
        data: base64Audio,
      }

      this.websocket!.send(JSON.stringify(message))
    } catch (error) {
      console.error('发送音频数据失败:', error)
      this.handleConnectionError(error as Error)
    }
  }

  /**
   * 播放音频
   */
  private async playAudio(base64Audio: string): Promise<void> {
    if (!this.audioContext) {
      console.error('音频上下文未初始化')
      return
    }

    try {
      const audioData = this.base64ToArrayBuffer(base64Audio)
      debugger
      // 播放PCM音频数据
      const buffer = await this.audioContext.decodeAudioData(audioData)
      const source = this.audioContext.createBufferSource()
      source.buffer = buffer
      source.connect(this.audioContext.destination)
      source.start()

      // 通知回调
      if (this.onAudioDataCallback) {
        this.onAudioDataCallback(audioData)
      }
    } catch (error) {
      console.error('音频播放失败:', error)
    }
  }

  /**
   * ArrayBuffer转Base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }

  /**
   * Base64转ArrayBuffer
   */
  private base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binaryString = atob(base64)
    const bytes = new Uint8Array(binaryString.length)
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i)
    }
    return bytes.buffer
  }

  private handleConnectionError(error: Error): void {
    console.error('连接错误:', error)
    this.updateConnectionState(ConnectionState.ERROR, undefined, error.message)

    if (this.onErrorCallback) {
      this.onErrorCallback(error.message)
    }
  }

  private shouldReconnect(): boolean {
    return (
      this.reconnectAttempts < this.config.maxReconnectAttempts &&
      this.connectionState !== ConnectionState.DISCONNECTED
    )
  }

  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    this.reconnectAttempts++
    this.updateConnectionState(ConnectionState.RECONNECTING)

    console.log(
      `准备重连 (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})，${this.config.reconnectInterval}ms后重试`,
    )

    this.reconnectTimer = window.setTimeout(() => {
      this.connect().catch((error) => {
        console.error('重连失败:', error)
        if (this.shouldReconnect()) {
          this.scheduleReconnect()
        } else {
          this.updateConnectionState(ConnectionState.ERROR, undefined, '重连次数已达上限')
        }
      })
    }, this.config.reconnectInterval)
  }

  private updateConnectionState(state: ConnectionState, latency?: number, error?: string): void {
    this.connectionState = state

    const status: ConnectionStatus = {
      state,
      reconnectAttempts: this.reconnectAttempts,
      lastError: error,
      latency,
    }

    if (this.onStateChangeCallback) {
      this.onStateChangeCallback(status)
    }
  }

  isConnected(): boolean {
    return (
      this.websocket !== null &&
      this.websocket.readyState === WebSocket.OPEN &&
      this.connectionState === ConnectionState.CONNECTED
    )
  }

  disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = undefined
    }

    if (this.websocket) {
      this.websocket.close(1000, '主动断开连接')
      this.websocket = null
    }

    this.updateConnectionState(ConnectionState.DISCONNECTED)
    console.log('WebSocket连接已断开')
  }

  // 事件回调设置方法
  onStateChange(callback: (status: ConnectionStatus) => void): void {
    this.onStateChangeCallback = callback
  }

  onAudioData(callback: (audioData: ArrayBuffer) => void): void {
    this.onAudioDataCallback = callback
  }

  onServerEvent(callback: (event: string) => void): void {
    this.onServerEventCallback = callback
  }

  onError(callback: (error: string) => void): void {
    this.onErrorCallback = callback
  }

  getStatus(): ConnectionStatus {
    return {
      state: this.connectionState,
      reconnectAttempts: this.reconnectAttempts,
      lastError: undefined,
      latency: undefined,
    }
  }

  updateConfig(config: Partial<WebSocketConfig>): void {
    this.config = { ...this.config, ...config }
  }

  destroy(): void {
    this.disconnect()

    if (this.audioContext) {
      this.audioContext.close()
      this.audioContext = null
    }

    // 清理所有回调
    this.onStateChangeCallback = undefined
    this.onAudioDataCallback = undefined
    this.onServerEventCallback = undefined
    this.onErrorCallback = undefined
  }
}
